# OPRecon v1.8-BETA
Using this you can find informations via PhoneInFoga(In-Built), Find location via IP Address & website link via IPGeoLocation(In-Built) and Phone Number Tracker(Made by @AbirHasan2005). Phone Number Tracker will give you IP Address using phone number. For more information join my Telegram Group.

## Telegram Group: http://t.me/linux_repo
Join Telegram Group for help and Feedback.

## Social Sites:
- Twitter: https://twitter.com/AbirHasan2005 [Only Follow]
- Instagram: https://instagram.com/AbirHasan2005 [Only Follow]
- Facebook: https://facebook.com/AbirHasan2005 [Only Follow]
- GitHub: https://github.com/AbirHasan2005 
- Telegram Group: http://t.me/linux_repo [Chat]

![Screenshot](https://github.com/AbirHasan2005/OPRecon/blob/master/capture.png)

## How to use:
- Compatible with:
	- Linux(Desktop)
	- Termux(Android)
- Requirements:
	- Python3
	- pip3
	- Git
- pip packages:
	- termcolor
	- colorama
	- requests
	- bs4
	- html5lib
	- phonenumbers
	- argparse
	- urllib3
- Commands:
```
git clone https://github.com/AbirHasan2005/OPRecon
cd OPRecon
python3 -m pip install -r requirements.txt
chmod +x *
bash run.sh
```

[![paypal](https://www.paypalobjects.com/en_US/i/btn/btn_donateCC_LG.gif)](https://paypal.me/AbirHasan2005)